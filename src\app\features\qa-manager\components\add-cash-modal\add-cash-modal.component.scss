// Clean Modal Design - Neutral Colors with Absa Accents
:root {
  --absa-red: #e60000;
  --absa-red-dark: #cc0000;
  --absa-red-light: #fef2f2;
  --absa-success: #059669;
  --absa-success-light: #f0fdf4;
  --absa-warning: #d97706;
  --absa-warning-light: #fffbeb;

  // Clean neutral colors for better readability
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius: 8px;
  --transition: all 0.2s ease;
}

// Clean Modal Container
.clean-modal-container {
  width: 100%;
  max-width: 1000px;
  min-width: 700px;
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  @media (max-width: 1024px) {
    min-width: 90vw;
    max-width: 95vw;
  }

  @media (max-width: 768px) {
    min-width: 95vw;
    max-width: 98vw;
  }
}

// Clean Header Design
.modal-header {
  background: var(--absa-red);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-light);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;

    .denomination-info {
      display: flex;
      align-items: center;
      gap: 1rem;

      .denomination-badge {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.25);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        flex-shrink: 0;

        .currency {
          font-size: 14px;
          margin-right: 2px;
        }

        .value {
          font-size: 20px;
          font-weight: 800;
        }
      }

      .title-section {
        .modal-title {
          margin: 0 0 0.25rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
        }

        .current-stock {
          margin: 0;
          font-size: 0.875rem;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 400;
        }
      }
    }

    .close-button {
      color: white;
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--radius);
      transition: var(--transition);
      flex-shrink: 0;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;

    .header-content {
      .denomination-info {
        gap: 0.75rem;

        .denomination-badge {
          width: 50px;
          height: 50px;

          .value {
            font-size: 16px;
          }
        }

        .title-section .modal-title {
          font-size: 1.125rem;
        }
      }
    }
  }
}

// Clean Content Layout
.modal-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
  background: var(--bg-primary);

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  // Section Styling
  .section {
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 1rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .optional {
        font-size: 0.875rem;
        font-weight: 400;
        color: var(--text-muted);
      }
    }
  }
}

// Action Buttons
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    &:hover {
      background: var(--bg-tertiary);
      border-color: var(--border-medium);
    }

    &.selected {
      background: var(--absa-red-light);
      border-color: var(--absa-red);
      color: var(--absa-red);

      mat-icon {
        color: var(--absa-red);
      }
    }

    @media (max-width: 480px) {
      padding: 0.875rem;
      font-size: 0.8125rem;
      gap: 0.5rem;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Quantity Type Selection
.quantity-type {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;

  .type-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      color: var(--text-muted);
    }

    .type-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .type-name {
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--text-primary);
        margin-bottom: 0.125rem;
      }

      .type-desc {
        font-size: 0.75rem;
        color: var(--text-muted);
      }
    }

    &:hover {
      background: var(--bg-tertiary);
      border-color: var(--border-medium);
    }

    &.selected {
      background: var(--absa-red-light);
      border-color: var(--absa-red);

      mat-icon {
        color: var(--absa-red);
      }

      .type-info {
        .type-name {
          color: var(--absa-red);
        }
      }
    }
  }
}

// Quantity Input Section
.quantity-input-section {
  .quantity-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .qty-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: var(--bg-secondary);
      border: 1px solid var(--border-light);
      border-radius: var(--radius);
      color: var(--text-secondary);
      cursor: pointer;
      transition: var(--transition);

      &:hover:not(:disabled) {
        background: var(--bg-tertiary);
        border-color: var(--border-medium);
        color: var(--text-primary);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .qty-input {
      width: 80px;
      height: 40px;
      text-align: center;
      border: 1px solid var(--border-light);
      border-radius: var(--radius);
      background: var(--bg-primary);
      color: var(--text-primary);
      font-size: 1rem;
      font-weight: 600;
      transition: var(--transition);

      &:focus {
        outline: none;
        border-color: var(--absa-red);
        box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.1);
      }

      // Remove spinner arrows
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--absa-transition);

    &.decrease {
      background: var(--absa-warning);
      color: white;

      &:hover:not(:disabled) {
        background: var(--absa-warning);
        transform: scale(1.1);
      }

      &:disabled {
        background: var(--absa-border);
        color: var(--absa-text-muted);
        cursor: not-allowed;
      }
    }

    &.increase {
      background: var(--absa-success);
      color: white;

      &:hover {
        background: var(--absa-success);
        transform: scale(1.1);
      }
    }

    mat-icon {
      font-size: 20px;
    }
  }

  .quantity-display {
    text-align: center;

    .quantity-input {
      width: 80px;
      height: 50px;
      border: 2px solid var(--absa-border);
      border-radius: var(--absa-radius);
      background: var(--absa-bg);
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      color: var(--absa-text);
      transition: var(--absa-transition);

      &:focus {
        outline: none;
        border-color: var(--absa-red);
      }

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }

    .quantity-label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: var(--absa-text-light);
      margin-top: 4px;
    }
  }
}

// Quick Select
.quick-select {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;

  .quick-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--absa-text-light);
    margin-right: 8px;
  }

  .quick-btn {
    padding: 6px 12px;
    border: 1px solid var(--absa-border);
    border-radius: var(--absa-radius);
    background: var(--absa-bg);
    color: var(--absa-text);
    font-weight: 500;
    cursor: pointer;
    transition: var(--absa-transition);
    font-size: 14px;

    &:hover {
      border-color: var(--absa-red);
      background: var(--absa-red-light);
    }

    &.selected {
      border-color: var(--absa-red);
      background: var(--absa-red);
      color: white;
    }
  }
}

// Quick Select and Summary Styling
.quick-amounts {
  .quick-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
  }

  .quick-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;

    .quick-btn {
      padding: 0.5rem 0.75rem;
      border: 1px solid var(--border-light);
      border-radius: 20px;
      background: var(--bg-secondary);
      color: var(--text-secondary);
      font-size: 0.8125rem;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      min-width: 40px;
      text-align: center;

      &:hover {
        border-color: var(--border-medium);
        background: var(--bg-tertiary);
      }

      &.selected {
        border-color: var(--absa-red);
        background: var(--absa-red);
        color: white;
      }
    }
  }
}

// Total Notes Display
.total-notes {
  text-align: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: var(--radius);
  border: 1px solid var(--border-light);

  .total-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
  }
}

// Summary Grid
.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;

  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius);
    text-align: center;

    .summary-label {
      font-size: 0.75rem;
      color: var(--text-muted);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 0.25rem;
    }

    .summary-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--text-primary);
    }

    &.adding {
      background: var(--absa-success-light);
      border-color: var(--absa-success);

      .summary-value {
        color: var(--absa-success);
      }
    }

    &.removing {
      background: var(--absa-warning-light);
      border-color: var(--absa-warning);

      .summary-value {
        color: var(--absa-warning);
      }
    }

    &.new-total {
      background: var(--absa-red-light);
      border-color: var(--absa-red);

      .summary-value {
        color: var(--absa-red);
      }
    }

    &.total-value {
      background: var(--bg-tertiary);
      border-color: var(--border-medium);

      .summary-value {
        color: var(--text-primary);
        font-size: 1rem;
      }
    }
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

// Summary Cards
.summary-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .summary-card {
    background: var(--absa-bg);
    border: 1px solid var(--absa-border);
    border-radius: 12px;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    &.current-stock {
      .summary-icon {
        background: var(--absa-bg-light);
        color: var(--absa-text);
      }
    }

    &.change-amount {
      &.adding {
        border-color: var(--absa-success);
        background: var(--absa-success-light);

        .summary-icon {
          background: var(--absa-success);
          color: white;
        }
      }

      &.removing {
        border-color: var(--absa-warning);
        background: var(--absa-warning-light);

        .summary-icon {
          background: var(--absa-warning);
          color: white;
        }
      }
    }

    &.new-total {
      border-color: var(--absa-red);
      background: var(--absa-red-light);

      .summary-icon {
        background: var(--absa-red);
        color: white;
      }
    }

    &.total-value {
      grid-column: 1 / -1;
      border-color: var(--absa-red);
      background: var(--absa-red-light);

      .summary-icon {
        background: var(--absa-red);
        color: white;
      }

      .summary-value.currency {
        font-size: 18px;
        font-weight: 700;
        color: var(--absa-red);
      }
    }

    .summary-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 20px;
      }
    }

    .summary-content {
      flex: 1;

      .summary-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: var(--absa-text-light);
        margin-bottom: 2px;
      }

      .summary-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: var(--absa-text);
      }
    }
  }

  // Progress Indicator
  .progress-indicator {
    margin-top: auto;
    padding: 1.5rem;
    background: var(--absa-bg);
    border-radius: 12px;
    border: 1px solid var(--absa-border);

    .progress-bar {
      width: 100%;
      height: 8px;
      background: var(--absa-border-light);
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0.75rem;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--absa-red) 0%, var(--absa-red-dark) 100%);
        border-radius: 4px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &.adding {
          background: linear-gradient(90deg, var(--absa-success) 0%, #22c55e 100%);
        }

        &.removing {
          background: linear-gradient(90deg, var(--absa-warning) 0%, #f59e0b 100%);
        }

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
          animation: shimmer 2s infinite;
        }
      }
    }

    .progress-labels {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .progress-label {
        font-size: 0.75rem;
        color: var(--absa-text-light);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .progress-value {
        font-size: 0.875rem;
        color: var(--absa-red);
        font-weight: 700;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Actions
.modal-actions {
  padding: 16px 0 0 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid var(--absa-border);
  margin-top: 24px;

  .cancel-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--absa-text-light);

    &:hover {
      background-color: var(--absa-bg-light);
    }
  }

  .submit-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-spinner {
      position: absolute;
      right: 8px;
    }
  }
}

// Global Modal Styles
::ng-deep {
  // Enhanced backdrop blur effect
  .add-cash-modal-backdrop {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(0, 0, 0, 0.6) !important;
    animation: fadeIn 0.3s ease-out;
  }

  // Wide modal panel
  .wide-modal-panel {
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;

    .mat-mdc-dialog-container {
      border-radius: 16px !important;
      overflow: hidden !important;
      padding: 0 !important;
      max-width: none !important;
      max-height: none !important;
    }
  }

  // Modern modal panel (fallback)
  .modern-modal-panel {
    border-radius: 16px !important;
    overflow: hidden !important;

    .mat-mdc-dialog-container {
      border-radius: 16px !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  // Form field styles
  .modal-container {
    .mat-mdc-form-field {
      .mat-mdc-form-field-outline {
        color: var(--absa-border) !important;
      }

      .mat-mdc-form-field-focus-overlay {
        background-color: transparent !important;
      }

      &.mat-focused {
        .mat-mdc-form-field-outline-thick {
          color: var(--absa-red) !important;
        }
      }

      .mat-mdc-select-value,
      .mat-mdc-input-element {
        color: var(--absa-text) !important;
      }

      .mat-mdc-form-field-hint {
        color: var(--absa-text-light) !important;
      }
    }
  }
}

// Enhanced Responsive Design
@media (max-width: 1024px) {
  .wide-modal-container {
    .content-grid {
      grid-template-columns: 1fr 320px;
    }
  }
}

@media (max-width: 768px) {
  .wide-modal-container {
    min-width: 95vw !important;
    max-width: 98vw !important;

    .content-grid {
      grid-template-columns: 1fr;
      max-height: none;

      .left-panel,
      .right-panel {
        padding: 1.5rem;
      }
    }

    .action-cards,
    .quantity-type-selector,
    .summary-cards {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .summary-cards .summary-card.total-value {
      grid-column: 1;
    }

    .modal-actions {
      flex-direction: column;
      gap: 0.75rem;
      padding: 1.5rem;

      .cancel-btn,
      .submit-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem;
      }
    }

    .progress-indicator {
      margin-top: 1rem;
      padding: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .wide-modal-container {
    min-width: 98vw !important;

    .content-grid {
      .left-panel,
      .right-panel {
        padding: 1rem;
      }
    }

    .modal-actions {
      padding: 1rem;
    }
  }
}

// Form Field Styling
.full-width {
  width: 100%;

  ::ng-deep {
    .mat-mdc-form-field {
      width: 100%;

      .mat-mdc-text-field-wrapper {
        background: var(--bg-primary);
        border-radius: var(--radius);

        .mat-mdc-form-field-outline {
          color: var(--border-light);
        }

        &.mdc-text-field--focused .mat-mdc-form-field-outline-thick {
          color: var(--absa-red);
        }
      }

      .mat-mdc-form-field-input-control {
        color: var(--text-primary);
        font-size: 0.875rem;

        &::placeholder {
          color: var(--text-muted);
        }
      }

      .mat-mdc-form-field-hint {
        color: var(--text-muted);
        font-size: 0.75rem;
      }
    }
  }
}

// Clean Modal Actions
.modal-actions {
  padding: 1.5rem 2rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    flex-direction: column-reverse;
  }

  .cancel-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border-medium);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;

    &:hover {
      border-color: var(--border-dark);
      background: var(--bg-tertiary);
      color: var(--text-primary);
    }

    @media (max-width: 768px) {
      width: 100%;
      justify-content: center;
    }
  }

  .submit-btn {
    padding: 0.75rem 1.5rem;
    background: var(--absa-red);
    color: white;
    border: 1px solid var(--absa-red);
    border-radius: var(--radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    font-size: 0.875rem;

    &:hover:not(:disabled) {
      background: var(--absa-red-dark);
      border-color: var(--absa-red-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    &:disabled {
      background: var(--border-medium);
      border-color: var(--border-medium);
      color: var(--text-muted);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .btn-spinner {
      position: absolute;
      right: 12px;
    }

    @media (max-width: 768px) {
      width: 100%;
      justify-content: center;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
