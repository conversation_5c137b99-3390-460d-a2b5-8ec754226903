import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AddCashModalComponent, AddCashData } from './add-cash-modal.component';
import { NoteSeries, NoteDenomination } from '../../../shared/models/inventory.model';

export interface AddCashResult {
  success: boolean;
  added?: number;
}

export interface AddCashDialogData {
  seriesName?: string;
  denomination?: number;
  currentQuantity?: number;
}

@Injectable({
  providedIn: 'root'
})
export class AddCashModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Add Cash modal
   * @param data Configuration data for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModal(data?: AddCashDialogData): Observable<AddCashResult | undefined> {
    // Convert the old interface to the new one
    const modalData: AddCashData = {};

    if (data?.seriesName) {
      // Map series name to enum
      const seriesMap: { [key: string]: NoteSeries } = {
        'mandela': NoteSeries.MANDELA,
        'big_5': NoteSeries.BIG_5,
        'commemorative': NoteSeries.COMMEMORATIVE,
        'v6': NoteSeries.V6
      };
      modalData.series = seriesMap[data.seriesName.toLowerCase()];
    }

    if (data?.denomination) {
      modalData.denomination = data.denomination as NoteDenomination;
    }

    const dialogRef: MatDialogRef<AddCashModalComponent> = this.dialog.open(
      AddCashModalComponent,
      {
        width: '900px',
        maxWidth: '95vw',
        maxHeight: '90vh',
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        hasBackdrop: true,
        backdropClass: 'add-cash-modal-backdrop',
        data: modalData,
        panelClass: ['add-cash-modal-panel', 'modern-modal-panel'],
        enterAnimationDuration: '400ms',
        exitAnimationDuration: '300ms'
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Add Cash modal with pre-selected series and denomination
   * @param series The series to pre-select
   * @param denomination The denomination to pre-select
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModalForDenomination(
    series: NoteSeries,
    denomination: NoteDenomination
  ): Observable<AddCashResult | undefined> {
    return this.openAddCashModal({
      seriesName: series,
      denomination: denomination
    });
  }

  /**
   * Checks if any Add Cash modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof AddCashModalComponent
    );
  }

  /**
   * Closes all open Add Cash modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof AddCashModalComponent)
      .forEach(dialog => dialog.close());
  }
}
