import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AddCashModalComponent, AddCashDialogData, AddCashResult } from './add-cash-modal.component';

@Injectable({
  providedIn: 'root'
})
export class AddCashModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Add Cash modal for a specific denomination
   * @param data Configuration data for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCashModal(data: AddCashDialogData): Observable<AddCashResult | undefined> {
    const dialogRef: MatDialogRef<AddCashModalComponent> = this.dialog.open(
      AddCashModalComponent,
      {
        width: '90vw',
        maxWidth: '1200px',
        minWidth: '800px',
        height: 'auto',
        maxHeight: '90vh',
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        hasBackdrop: true,
        backdropClass: 'add-cash-modal-backdrop',
        data: data,
        panelClass: ['add-cash-modal-panel', 'modern-modal-panel', 'wide-modal-panel'],
        enterAnimationDuration: '400ms',
        exitAnimationDuration: '300ms'
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Checks if any Add Cash modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof AddCashModalComponent
    );
  }

  /**
   * Closes all open Add Cash modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof AddCashModalComponent)
      .forEach(dialog => dialog.close());
  }
}
