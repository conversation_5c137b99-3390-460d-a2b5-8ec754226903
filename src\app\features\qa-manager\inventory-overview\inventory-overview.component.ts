import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

// QA Manager Components
import { AddCashModalService } from '../components/add-cash-modal/add-cash-modal.service';
import type { AddCashResult, AddCashDialogData } from '../components/add-cash-modal/add-cash-modal.component';
import { AddCoinModalService } from '../components/add-coin-modal/add-coin-modal.service';
import type { AddCoinResult, AddCoinDialogData } from '../components/add-coin-modal/add-coin-modal.component';

// Inventory Models
import {
  CoinDenomination,
  CoinInventory,
  COIN_DENOMINATION_LABELS,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES
} from '../../../shared/models/inventory.model';

@Component({
  selector: 'app-inventory-overview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatSnackBarModule,
    MatTooltipModule
  ],
  templateUrl: './inventory-overview.component.html',
  styleUrls: ['./inventory-overview.component.scss']
})
export class InventoryOverviewComponent implements OnInit {
  // Mock data for demonstration
  inventorySummary = {
    totalValue: 1250000,
    totalNotes: 12500,
    lowStockAlerts: [
      { inventoryId: '1', severity: 'high' as const, message: 'R200 notes running low' }
    ]
  };

  // Mock inventory data - will be redesigned
  inventoryData: any[] = [];

  // Series data for detailed inventory
  seriesData = [
    {
      id: 'mandela',
      name: 'Mandela Series',
      totalBatches: 122,
      totalSingles: 77,
      totalValue: 854040.00,
      denominations: [
        {
          value: 10,
          batches: 20,
          singles: 87,
          totalValue: 20870.00,
          stockLevel: 100
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 30,
          singles: 79,
          totalValue: 153950.00,
          stockLevel: 100
        },
        {
          value: 100,
          batches: 32,
          singles: 61,
          totalValue: 65220.00,
          stockLevel: 100
        },
        {
          value: 200,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        }
      ]
    },
    {
      id: 'big5',
      name: 'Big 5 Series',
      totalBatches: 95,
      totalSingles: 43,
      totalValue: 720500.00,
      denominations: [
        {
          value: 10,
          batches: 5,
          singles: 12,
          totalValue: 512.00,
          stockLevel: 25
        },
        {
          value: 20,
          batches: 25,
          singles: 43,
          totalValue: 50860.00,
          stockLevel: 85
        },
        {
          value: 50,
          batches: 8,
          singles: 15,
          totalValue: 4015.00,
          stockLevel: 35
        },
        {
          value: 100,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 200,
          batches: 18,
          singles: 22,
          totalValue: 362200.00,
          stockLevel: 45
        }
      ]
    },
    {
      id: 'commemorative',
      name: 'Commemorative Series',
      totalBatches: 45,
      totalSingles: 23,
      totalValue: 125000.00,
      denominations: [
        {
          value: 10,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 20,
          batches: 3,
          singles: 8,
          totalValue: 608.00,
          stockLevel: 15
        },
        {
          value: 50,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 100,
          batches: 12,
          singles: 8,
          totalValue: 120800.00,
          stockLevel: 75
        },
        {
          value: 200,
          batches: 2,
          singles: 5,
          totalValue: 4005.00,
          stockLevel: 8
        }
      ]
    },
    {
      id: 'v6',
      name: 'V6 Series',
      totalBatches: 67,
      totalSingles: 34,
      totalValue: 890000.00,
      denominations: [
        {
          value: 10,
          batches: 15,
          singles: 45,
          totalValue: 1545.00,
          stockLevel: 65
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 12,
          singles: 33,
          totalValue: 6033.00,
          stockLevel: 55
        },
        {
          value: 100,
          batches: 22,
          singles: 22,
          totalValue: 222200.00,
          stockLevel: 90
        },
        {
          value: 200,
          batches: 15,
          singles: 12,
          totalValue: 302400.00,
          stockLevel: 60
        }
      ]
    }
  ];

  DENOMINATION_LABELS: { [key: number]: string } = {
    10: 'R10',
    20: 'R20',
    50: 'R50',
    100: 'R100',
    200: 'R200'
  };

  // Coin inventory data
  coinInventory: CoinInventory[] = [
    {
      id: 'coin-c10',
      denomination: CoinDenomination.C10,
      quantity: 2500,
      batches: 25,
      value: 250.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    },
    {
      id: 'coin-c20',
      denomination: CoinDenomination.C20,
      quantity: 2000,
      batches: 40,
      value: 400.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    },
    {
      id: 'coin-c50',
      denomination: CoinDenomination.C50,
      quantity: 1500,
      batches: 75,
      value: 750.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    },
    {
      id: 'coin-r1',
      denomination: CoinDenomination.R1,
      quantity: 1000,
      batches: 50,
      value: 1000.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    },
    {
      id: 'coin-r2',
      denomination: CoinDenomination.R2,
      quantity: 800,
      batches: 32,
      value: 1600.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    },
    {
      id: 'coin-r5',
      denomination: CoinDenomination.R5,
      quantity: 200,
      batches: 10,
      value: 1000.00,
      lastUpdated: new Date(),
      updatedBy: 'system'
    }
  ];

  // Coin configuration
  coinDenominations = Object.values(CoinDenomination);
  coinLabels = COIN_DENOMINATION_LABELS;
  coinBatchConfig = COIN_BATCH_CONFIG;
  coinBatchValues = COIN_BATCH_VALUES;

  // Mock user service for demo
  userService = {
    hasManagerPrivileges: () => true
  };

  constructor(
    private addCashModalService: AddCashModalService,
    private addCoinModalService: AddCoinModalService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Component initialization
  }

  // Tab change handler
  onTabChange(event: any): void {
    console.log('Tab changed to:', event.index);
  }

  // Track by function for denomination cards
  trackByDenomination(index: number, denomination: any): any {
    return denomination.value;
  }

  // Get status class based on stock level
  getStatusClass(stockLevel: number): string {
    if (stockLevel === 0) return 'status-out-of-stock';
    if (stockLevel >= 80) return 'status-good';
    if (stockLevel >= 50) return 'status-medium';
    return 'status-low';
  }

  // Get status text based on stock level
  getStatusText(stockLevel: number): string {
    if (stockLevel === 0) return 'Out of Stock';
    if (stockLevel >= 80) return 'In Stock';
    if (stockLevel >= 50) return 'Medium';
    return 'Low Stock';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: number): string {
    return this.DENOMINATION_LABELS[denomination] || `R${denomination}`;
  }

  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }



  getStockStatus(item: any): { status: string; class: string } {
    if (item.quantity < 100) {
      return { status: 'Low Stock', class: 'low-stock' };
    } else if (item.quantity > 500) {
      return { status: 'High Stock', class: 'high-stock' };
    }
    return { status: 'Normal', class: 'normal-stock' };
  }

  getStockStatusIcon(item: any): string {
    const status = this.getStockStatus(item);
    if (status.class === 'low-stock') return 'warning';
    if (status.class === 'high-stock') return 'trending_up';
    return 'check_circle';
  }

  getStockPercentage(item: any): number {
    const maxStock = 1000; // Assume max stock is 1000
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: any): string {
    const percentage = this.getStockPercentage(item);
    if (percentage < 20) return 'warn';
    if (percentage > 80) return 'accent';
    return 'primary';
  }

  getSeriesStyleClass(series: string): string {
    return `series-${series.toLowerCase()}`;
  }

  getDenominationImage(denomination: number): string {
    return `assets/images/Money/R${denomination}.jpg`;
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  onAddCash(seriesId?: string, denominationValue?: number): void {
    if (!seriesId || !denominationValue) {
      console.error('Series ID and denomination value are required');
      return;
    }

    // Get current quantity for this denomination
    const currentQuantity = this.getCurrentQuantity(seriesId, denominationValue);

    // Configure modal data
    const modalData: AddCashDialogData = {
      seriesName: seriesId,
      denomination: denominationValue,
      currentQuantity: currentQuantity
    };

    // Open the modal
    this.addCashModalService.openAddCashModal(modalData)
      .subscribe(result => {
        if (result) {
          this.handleAddCashResult(result, seriesId, denominationValue);
        }
      });
  }

  private getCurrentQuantity(seriesId: string, denominationValue: number): number {
    // This would normally come from your data service
    // For now, return a mock value based on the denomination
    const mockQuantities: { [key: string]: number } = {
      'Mandela-10': 87,
      'Mandela-20': 0,
      'Mandela-50': 79,
      'Mandela-100': 61,
      'Mandela-200': 0
    };

    return mockQuantities[`${seriesId}-${denominationValue}`] || 0;
  }

  private handleAddCashResult(result: AddCashResult, seriesId: string, denominationValue: number): void {
    const action = result.action === 'add' ? 'Added' : 'Removed';
    const totalValue = result.quantity * denominationValue;

    // Show success message
    this.snackBar.open(
      `${action} ${result.quantity} x R${denominationValue} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash transaction completed:', {
      result,
      seriesId,
      denominationValue,
      timestamp: new Date().toISOString()
    });

    // Here you would typically call a service to update the backend
    // this.inventoryService.updateCashInventory(result);

    // For now, we'll just refresh the component data
    this.refreshInventoryData();
  }

  private refreshInventoryData(): void {
    // In a real application, you would reload data from the backend
    // For now, we'll just log that a refresh would happen
    console.log('Inventory data would be refreshed from backend');
  }

  // Methods will be redesigned from scratch

  trackByItemId(index: number, item: any): string {
    return item.id || index.toString();
  }

  viewDetails(item: any): void {
    // Navigate to detailed view or open modal
    console.log('View details for item:', item);
    this.snackBar.open(
      `Viewing details for ${this.getDenominationLabel(item.denomination)}`,
      'Close',
      { duration: 2000 }
    );
  }

  /**
   * Opens the Add Coin modal for a specific denomination
   */
  openAddCoinModal(denomination: CoinDenomination): void {
    const coinData = this.coinInventory.find(coin => coin.denomination === denomination);
    if (!coinData) {
      this.snackBar.open('Coin data not found', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const dialogData: AddCoinDialogData = {
      denomination,
      currentQuantity: coinData.quantity,
      currentBatches: coinData.batches
    };

    this.addCoinModalService.openAddCoinModal(dialogData).subscribe({
      next: (result: AddCoinResult | undefined) => {
        if (result) {
          this.handleCoinModalResult(result);
        }
      },
      error: (error) => {
        console.error('Error opening add coin modal:', error);
        this.snackBar.open('Failed to open coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Handles the result from the Add Coin modal
   */
  private handleCoinModalResult(result: AddCoinResult): void {
    const coinIndex = this.coinInventory.findIndex(coin => coin.denomination === result.denomination);

    if (coinIndex === -1) {
      this.snackBar.open('Coin not found in inventory', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const coin = this.coinInventory[coinIndex];
    const oldQuantity = coin.quantity;

    // Update the coin inventory
    if (result.action === 'add') {
      coin.quantity += result.quantity;
      coin.batches += result.batches;
    } else {
      coin.quantity = Math.max(0, coin.quantity - result.quantity);
      coin.batches = Math.max(0, coin.batches - result.batches);
    }

    coin.value = coin.quantity * result.denomination;
    coin.lastUpdated = new Date();
    coin.updatedBy = 'current-user'; // In real app, get from auth service

    // Show success message
    const action = result.action === 'add' ? 'Added' : 'Removed';
    const coinLabel = this.coinLabels[result.denomination];
    this.snackBar.open(
      `${action} ${result.quantity} ${coinLabel} coins (${oldQuantity} → ${coin.quantity})`,
      'Close',
      { duration: 4000, panelClass: ['success-snackbar'] }
    );
  }

  /**
   * Gets the coin label for display
   */
  getCoinLabel(denomination: CoinDenomination): string {
    return this.coinLabels[denomination];
  }

  /**
   * Formats coin value for display
   */
  formatCoinValue(value: number): string {
    if (value >= 1) {
      return `R${value.toFixed(0)}`;
    } else {
      return `${Math.round(value * 100)}c`;
    }
  }

  /**
   * Gets the total coin inventory value
   */
  getTotalCoinValue(): number {
    return this.coinInventory.reduce((total, coin) => total + coin.value, 0);
  }

  /**
   * Gets the total number of coins
   */
  getTotalCoins(): number {
    return this.coinInventory.reduce((total, coin) => total + coin.quantity, 0);
  }

  /**
   * Gets coins with low stock
   */
  getLowStockCoins(): CoinInventory[] {
    return this.coinInventory.filter(coin => this.getCoinStockLevel(coin) !== 'normal');
  }

  /**
   * Gets the stock level for a coin
   */
  getCoinStockLevel(coin: CoinInventory): 'normal' | 'low' | 'out-of-stock' {
    if (coin.quantity === 0) {
      return 'out-of-stock';
    } else if (coin.quantity < 500) {
      return 'low';
    } else {
      return 'normal';
    }
  }
}
