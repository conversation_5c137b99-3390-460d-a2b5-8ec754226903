import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CommonModule } from '@angular/common';

export interface AddCashDialogData {
  seriesName: string;
  denomination: number;
  currentQuantity: number;
}

export interface AddCashResult {
  quantity: number;
  action: 'add' | 'remove';
  notes?: string;
}

@Component({
  selector: 'app-add-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './add-cash-modal.component.html',
  styleUrls: ['./add-cash-modal.component.scss']
})
export class AddCashModalComponent {
  addCashForm: FormGroup;
  isSubmitting = false;
  quantityType: 'batches' | 'singles' = 'singles';

  // Quick amounts for batches and singles
  batchQuickAmounts = [1, 2, 5, 10, 20];
  singlesQuickAmounts = [5, 10, 25, 50, 100];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AddCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCashDialogData
  ) {
    this.addCashForm = this.fb.group({
      action: ['add', Validators.required],
      batchQuantity: [1, [Validators.required, Validators.min(1)]],
      singlesQuantity: [1, [Validators.required, Validators.min(1)]],
      notes: ['', [Validators.maxLength(500)]]
    });
  }

  onSubmit(): void {
    if (this.addCashForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      // Add a small delay for better UX with loading animation
      setTimeout(() => {
        const result: AddCashResult = {
          quantity: this.getTotalNotes(),
          action: this.addCashForm.get('action')?.value,
          notes: this.addCashForm.get('notes')?.value
        };
        this.dialogRef.close(result);
      }, 800);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  selectAction(action: 'add' | 'remove'): void {
    this.addCashForm.patchValue({ action });
  }

  setQuantityType(type: 'batches' | 'singles'): void {
    this.quantityType = type;
  }

  getQuantityValue(): number {
    const controlName = this.quantityType === 'batches' ? 'batchQuantity' : 'singlesQuantity';
    return this.addCashForm.get(controlName)?.value || 1;
  }

  setQuantityValue(amount: number): void {
    const controlName = this.quantityType === 'batches' ? 'batchQuantity' : 'singlesQuantity';
    this.addCashForm.patchValue({ [controlName]: amount });
  }

  adjustQuantity(delta: number): void {
    const currentValue = this.getQuantityValue();
    const newValue = Math.max(1, currentValue + delta);
    this.setQuantityValue(newValue);
  }

  onQuantityInputChange(event: any): void {
    const value = parseInt(event.target.value) || 1;
    const validValue = Math.max(1, value);
    this.setQuantityValue(validValue);

    // Update the input field if the value was corrected
    if (value !== validValue) {
      event.target.value = validValue;
    }
  }

  getQuickAmounts(): number[] {
    return this.quantityType === 'batches' ? this.batchQuickAmounts : this.singlesQuickAmounts;
  }

  getTotalNotes(): number {
    const batchQuantity = this.addCashForm.get('batchQuantity')?.value || 0;
    const singlesQuantity = this.addCashForm.get('singlesQuantity')?.value || 0;

    if (this.quantityType === 'batches') {
      return batchQuantity * 100; // 1 batch = 100 notes
    } else {
      return singlesQuantity; // 1 single = 1 note
    }
  }

  getNewTotal(): number {
    const totalNotes = this.getTotalNotes();
    const action = this.addCashForm.get('action')?.value;

    if (action === 'add') {
      return this.data.currentQuantity + totalNotes;
    } else {
      return Math.max(0, this.data.currentQuantity - totalNotes);
    }
  }

  getTotalValue(): number {
    return this.getNewTotal() * this.data.denomination;
  }
}
