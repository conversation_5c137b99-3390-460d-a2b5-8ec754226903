<div class="clean-modal-container">
  <!-- Clean Header -->
  <div class="modal-header">
    <div class="header-content">
      <div class="denomination-info">
        <div class="denomination-badge">
          <span class="currency">R</span>
          <span class="value">{{ data.denomination }}</span>
        </div>
        <div class="title-section">
          <h2 class="modal-title">{{ data.seriesName }} Series</h2>
          <p class="current-stock">{{ data.currentQuantity }} notes in stock</p>
        </div>
      </div>
      <button mat-icon-button class="close-button" (click)="onCancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div mat-dialog-content class="modal-content">
    <form [formGroup]="addCashForm" (ngSubmit)="onSubmit()">

      <!-- Action Selection -->
      <div class="section">
        <h3 class="section-title">Choose Action</h3>
        <div class="action-buttons">
          <button type="button"
                  class="action-btn"
                  [class.selected]="addCashForm.get('action')?.value === 'add'"
                  (click)="selectAction('add')">
            <mat-icon>add_circle_outline</mat-icon>
            <span>Add Cash</span>
          </button>
          <button type="button"
                  class="action-btn"
                  [class.selected]="addCashForm.get('action')?.value === 'remove'"
                  (click)="selectAction('remove')">
            <mat-icon>remove_circle_outline</mat-icon>
            <span>Remove Cash</span>
          </button>
        </div>
      </div>

      <!-- Quantity Selection -->
      <div class="section">
        <h3 class="section-title">Quantity</h3>

        <!-- Quantity Type -->
        <div class="quantity-type">
          <button type="button"
                  class="type-btn"
                  [class.selected]="quantityType === 'batches'"
                  (click)="setQuantityType('batches')">
            <mat-icon>inventory</mat-icon>
            <div class="type-info">
              <span class="type-name">Batches</span>
              <span class="type-desc">1 batch = 100 notes</span>
            </div>
          </button>

          <button type="button"
                  class="type-btn"
                  [class.selected]="quantityType === 'singles'"
                  (click)="setQuantityType('singles')">
            <mat-icon>looks_one</mat-icon>
            <div class="type-info">
              <span class="type-name">Singles</span>
              <span class="type-desc">1 single = 1 note</span>
            </div>
          </button>
        </div>

        <!-- Quantity Input -->
        <div class="quantity-input-section">
          <div class="quantity-controls">
            <button type="button" class="qty-btn" (click)="adjustQuantity(-1)" [disabled]="getQuantityValue() <= 1">
              <mat-icon>remove</mat-icon>
            </button>
            <input type="number"
                   class="qty-input"
                   [value]="getQuantityValue()"
                   (input)="onQuantityInputChange($event)"
                   min="1">
            <button type="button" class="qty-btn" (click)="adjustQuantity(1)">
              <mat-icon>add</mat-icon>
            </button>
          </div>

          <!-- Quick Select -->
          <div class="quick-amounts">
            <span class="quick-label">Quick select:</span>
            <div class="quick-buttons">
              <button type="button"
                      *ngFor="let amount of getQuickAmounts()"
                      class="quick-btn"
                      [class.selected]="getQuantityValue() === amount"
                      (click)="setQuantityValue(amount)">
                {{ amount }}
              </button>
            </div>
          </div>

          <!-- Total Notes Display -->
          <div class="total-notes">
            <span class="total-text">Total: {{ getTotalNotes() }} notes</span>
          </div>
        </div>
      </div>

      <!-- Summary Section -->
      <div class="section">
        <h3 class="section-title">Summary</h3>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">Current Stock</span>
            <span class="summary-value">{{ data.currentQuantity }}</span>
          </div>
          <div class="summary-item" [class.adding]="addCashForm.get('action')?.value === 'add'" [class.removing]="addCashForm.get('action')?.value === 'remove'">
            <span class="summary-label">{{ addCashForm.get('action')?.value === 'add' ? 'Adding' : 'Removing' }}</span>
            <span class="summary-value">{{ getTotalNotes() }}</span>
          </div>
          <div class="summary-item new-total">
            <span class="summary-label">New Total</span>
            <span class="summary-value">{{ getNewTotal() }}</span>
          </div>
          <div class="summary-item total-value">
            <span class="summary-label">Total Value</span>
            <span class="summary-value">{{ getTotalValue() | currency:'ZAR':'symbol':'1.2-2' }}</span>
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div class="section">
        <h3 class="section-title">Additional Notes <span class="optional">(Optional)</span></h3>
        <mat-form-field appearance="outline" class="full-width">
          <textarea matInput
                    formControlName="notes"
                    placeholder="Add any additional notes, reasons, or comments..."
                    rows="3"
                    maxlength="500"></textarea>
          <mat-hint>{{ (addCashForm.get('notes')?.value || '').length }}/500 characters</mat-hint>
        </mat-form-field>
      </div>
    </form>

  <!-- Actions -->
  <div mat-dialog-actions class="modal-actions">
    <button mat-button type="button" (click)="onCancel()" class="cancel-btn">
      <mat-icon>close</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            color="primary"
            (click)="onSubmit()"
            [disabled]="!addCashForm.valid || isSubmitting"
            class="submit-btn">
      <mat-icon>{{ addCashForm.get('action')?.value === 'add' ? 'add_circle' : 'remove_circle' }}</mat-icon>
      {{ addCashForm.get('action')?.value === 'add' ? 'Add Cash' : 'Remove Cash' }}
      <mat-spinner *ngIf="isSubmitting" diameter="20" class="btn-spinner"></mat-spinner>
    </button>
  </div>
</div>
